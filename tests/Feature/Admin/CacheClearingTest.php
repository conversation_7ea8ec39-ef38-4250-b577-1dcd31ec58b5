<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Services\CacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CacheClearingTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user with valid admin email and proper status
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Admin User',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Create regular user
        $this->regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Regular User',
        ]);
    }

    public function test_admin_can_clear_cache(): void
    {
        // Set some cache values to test clearing
        Cache::put('test_cache_key', 'test_value', 3600);
        Cache::put('admin_stats', ['test' => 'data'], 3600);
        
        // Verify cache is set
        $this->assertEquals('test_value', Cache::get('test_cache_key'));
        $this->assertNotNull(Cache::get('admin_stats'));

        // Make request as admin
        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/dashboard/clear-cache');

        // Assert successful response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'All caches cleared successfully!'
            ]);

        // Verify caches are cleared
        $this->assertNull(Cache::get('test_cache_key'));
        $this->assertNull(Cache::get('admin_stats'));
    }

    public function test_regular_user_cannot_clear_cache(): void
    {
        // Make request as regular user
        $response = $this->actingAs($this->regularUser)
            ->postJson('/admin/dashboard/clear-cache');

        // Should be forbidden
        $response->assertStatus(403);
    }

    public function test_guest_cannot_clear_cache(): void
    {
        // Make request as guest
        $response = $this->postJson('/admin/dashboard/clear-cache');

        // Should be unauthorized
        $response->assertStatus(401);
    }

    public function test_cache_clearing_logs_activity(): void
    {
        // Make request as admin
        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/dashboard/clear-cache');

        // Assert successful response
        $response->assertStatus(200);

        // Check that activity was logged
        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $this->adminUser->id,
            'activity_type' => 'admin_cache_cleared',
            'description' => 'All application caches cleared via admin dashboard',
        ]);
    }

    public function test_cache_clearing_handles_exceptions_gracefully(): void
    {
        // Mock CacheService to throw exception
        $this->mock(CacheService::class, function ($mock) {
            $mock->shouldReceive('clearSearchCaches')->andThrow(new \Exception('Cache service error'));
            $mock->shouldReceive('clearAdminCaches')->andThrow(new \Exception('Cache service error'));
        });

        // Make request as admin
        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/dashboard/clear-cache');

        // Should return error response
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to clear caches. Please try again.'
            ]);
    }

    public function test_cache_clearing_calls_artisan_commands(): void
    {
        // Mock Artisan facade
        Artisan::shouldReceive('call')
            ->with('cache:clear')
            ->once();
        
        Artisan::shouldReceive('call')
            ->with('config:clear')
            ->once();
            
        Artisan::shouldReceive('call')
            ->with('route:clear')
            ->once();
            
        Artisan::shouldReceive('call')
            ->with('view:clear')
            ->once();
            
        Artisan::shouldReceive('call')
            ->with('event:clear')
            ->once();

        // Make request as admin
        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/dashboard/clear-cache');

        // Assert successful response
        $response->assertStatus(200);
    }
}
