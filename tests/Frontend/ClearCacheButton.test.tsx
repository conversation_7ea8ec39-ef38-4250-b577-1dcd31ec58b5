import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { AppSidebarHeader } from '@/components/app-sidebar-header';

// Mock sonner toast
vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock useAppearance hook
vi.mock('@/hooks/use-appearance', () => ({
    useAppearance: () => ({
        appearance: 'light',
        setAppearance: vi.fn(),
    }),
}));

describe('Clear Cache Button', () => {
    beforeEach(() => {
        vi.clearAllMocks();

        // Mock admin user by default
        vi.mocked(global.mockRouter.post).mockImplementation(vi.fn());
    });

    it('renders clear cache button for admin users', () => {
        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        expect(clearCacheButton).toBeInTheDocument();
        expect(clearCacheButton).toHaveAttribute('title', 'Clear All Caches');
    });

    it('does not render clear cache button for non-admin users', () => {
        // Mock non-admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Regular User',
                    },
                },
            },
            url: '/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.queryByTitle('Clear All Caches');
        expect(clearCacheButton).not.toBeInTheDocument();
    });

    it('calls clear cache endpoint when button is clicked', async () => {
        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        expect(global.mockRouter.post).toHaveBeenCalledWith(
            '/admin/dashboard/clear-cache',
            {},
            expect.objectContaining({
                onSuccess: expect.any(Function),
                onError: expect.any(Function),
                onFinish: expect.any(Function),
            })
        );
    });

    it('disables button during cache clearing', async () => {
        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');

        // Click the button
        fireEvent.click(clearCacheButton);

        // Button should be disabled during the request
        expect(clearCacheButton).toBeDisabled();
    });

    it('shows loading animation during cache clearing', async () => {
        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        // Check if the icon has the animate-pulse class
        const icon = clearCacheButton.querySelector('svg');
        expect(icon).toHaveClass('animate-pulse');
    });

    it('handles successful cache clearing', async () => {
        const { toast } = require('sonner');

        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        // Simulate successful response
        const onSuccessCallback = global.mockRouter.post.mock.calls[0][2].onSuccess;
        onSuccessCallback();

        expect(toast.success).toHaveBeenCalledWith('All caches cleared successfully!');
    });

    it('handles cache clearing errors', async () => {
        const { toast } = require('sonner');

        // Mock admin user
        const { usePage } = require('@inertiajs/react');
        usePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        email: '<EMAIL>',
                        name: 'Admin User',
                    },
                },
            },
            url: '/admin/dashboard',
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        // Simulate error response
        const onErrorCallback = global.mockRouter.post.mock.calls[0][2].onError;
        onErrorCallback();

        expect(toast.error).toHaveBeenCalledWith('Failed to clear caches. Please try again.');
    });
});
